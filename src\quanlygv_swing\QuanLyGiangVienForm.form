<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
    <Property name="title" type="java.lang.String" value="Qu&#x1ea3;n L&#xfd; Gi&#x1ea3;ng Vi&#xea;n"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="true"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <EmptySpace min="20" pref="20" max="20" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="lblTitle" pref="560" max="32767" attributes="0"/>
                  <Group type="102" attributes="0">
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="lblMaGV" min="-2" max="-2" attributes="0"/>
                          <Component id="lblTenGV" min="-2" max="-2" attributes="0"/>
                          <Component id="lblDiaChi" min="-2" max="-2" attributes="0"/>
                          <Component id="lblHeSoLuong" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace min="20" pref="20" max="20" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="txtMaGV" max="32767" attributes="0"/>
                          <Component id="txtTenGV" max="32767" attributes="0"/>
                          <Component id="txtDiaChi" max="32767" attributes="0"/>
                          <Component id="txtHeSoLuong" max="32767" attributes="0"/>
                      </Group>
                  </Group>
                  <Component id="jScrollPane1" max="32767" attributes="0"/>
                  <Group type="102" attributes="0">
                      <Component id="btnThem" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="btnSua" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="btnXoa" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="btnTim" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="btnTruoc" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="btnSau" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="btnHuy" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="btnThoat" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace min="20" pref="20" max="20" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <EmptySpace min="20" pref="20" max="20" attributes="0"/>
              <Component id="lblTitle" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="20" pref="20" max="20" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="lblMaGV" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txtMaGV" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="10" pref="10" max="10" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="lblTenGV" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txtTenGV" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="10" pref="10" max="10" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="lblDiaChi" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txtDiaChi" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="10" pref="10" max="10" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="lblHeSoLuong" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txtHeSoLuong" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="20" pref="20" max="20" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="btnThem" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="btnSua" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="btnXoa" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="btnTim" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="btnTruoc" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="btnSau" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="btnHuy" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="btnThoat" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="20" pref="20" max="20" attributes="0"/>
              <Component id="jScrollPane1" pref="200" max="32767" attributes="0"/>
              <EmptySpace min="20" pref="20" max="20" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="lblTitle">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="18" style="1"/>
        </Property>
        <Property name="horizontalAlignment" type="int" value="0"/>
        <Property name="text" type="java.lang.String" value="QU&#x1ea2;N L&#xdd; GI&#x1ea2;NG VI&#xca;N"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lblMaGV">
      <Properties>
        <Property name="text" type="java.lang.String" value="M&#xe3; Gi&#x1ea3;ng Vi&#xea;n:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txtMaGV">
    </Component>
    <Component class="javax.swing.JLabel" name="lblTenGV">
      <Properties>
        <Property name="text" type="java.lang.String" value="T&#xea;n Gi&#x1ea3;ng Vi&#xea;n:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txtTenGV">
    </Component>
    <Component class="javax.swing.JLabel" name="lblDiaChi">
      <Properties>
        <Property name="text" type="java.lang.String" value="&#x110;&#x1ecb;a Ch&#x1ec9;:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txtDiaChi">
    </Component>
    <Component class="javax.swing.JLabel" name="lblHeSoLuong">
      <Properties>
        <Property name="text" type="java.lang.String" value="H&#x1ec7; S&#x1ed1; L&#x1b0;&#x1a1;ng:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txtHeSoLuong">
    </Component>
    <Component class="javax.swing.JButton" name="btnThem">
      <Properties>
        <Property name="text" type="java.lang.String" value="Th&#xea;m"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnThemActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnSua">
      <Properties>
        <Property name="text" type="java.lang.String" value="S&#x1eed;a"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnSuaActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnXoa">
      <Properties>
        <Property name="text" type="java.lang.String" value="X&#xf3;a"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnXoaActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnTim">
      <Properties>
        <Property name="text" type="java.lang.String" value="T&#xec;m"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnTimActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnTruoc">
      <Properties>
        <Property name="text" type="java.lang.String" value="Tr&#x1b0;&#x1edb;c"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnTruocActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnSau">
      <Properties>
        <Property name="text" type="java.lang.String" value="Sau"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnSauActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnHuy">
      <Properties>
        <Property name="text" type="java.lang.String" value="H&#x1ee7;y"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnHuyActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnThoat">
      <Properties>
        <Property name="text" type="java.lang.String" value="Tho&#xe1;t"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnThoatActionPerformed"/>
      </Events>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="tblGiangVien">
          <Properties>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="4" rowCount="0">
                <Column editable="true" title="M&#xe3; GV" type="java.lang.Object"/>
                <Column editable="true" title="T&#xea;n GV" type="java.lang.Object"/>
                <Column editable="true" title="&#x110;&#x1ecb;a ch&#x1ec9;" type="java.lang.Object"/>
                <Column editable="true" title="H&#x1ec7; s&#x1ed1; l&#x1b0;&#x1a1;ng" type="java.lang.Object"/>
              </Table>
            </Property>
          </Properties>
          <Events>
            <EventHandler event="mouseClicked" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="tblGiangVienMouseClicked"/>
          </Events>
        </Component>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
