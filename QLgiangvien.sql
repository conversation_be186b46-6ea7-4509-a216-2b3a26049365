-- <PERSON><PERSON><PERSON> cơ sở dữ liệu
CREATE DATABASE IF NOT EXISTS QLgiangvien;

-- <PERSON><PERSON> dụng cơ sở dữ liệu
USE QLgiangvien;

-- Tạ<PERSON> bảng TL_gvien
CREATE TABLE IF NOT EXISTS TL_gvien (
    magv VARCHAR(10) PRIMARY KEY,
    tengv VARCHAR(50) NOT NULL,
    dchi VARCHAR(100),
    hsluong FLOAT
);

-- Thêm dữ liệu mẫu (5 bản ghi)
INSERT INTO TL_gvien (magv, tengv, dchi, hsluong) VALUES
('GV01', '<PERSON>r<PERSON><PERSON>', '<PERSON>', 4.5),
('GV02', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 5.0),
('GV03', '<PERSON><PERSON> T<PERSON>ị <PERSON>', '<PERSON><PERSON><PERSON>', 4.2),
('GV04', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 4.8),
('GV05', 'Đỗ <PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 4.6);