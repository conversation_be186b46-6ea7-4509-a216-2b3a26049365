package quanlygv_swing;

import java.sql.*;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.util.Vector;

/**
 *
 * <AUTHOR>
 */
public class QuanLyGiangVienForm extends javax.swing.JFrame {
    private Connection conn;
    private PreparedStatement pst;
    private ResultSet rs;
    private DefaultTableModel dtm;
    private int currentRow = 0;



    /**
     * Creates new form QuanLyGiangVienForm
     */
    public QuanLyGiangVienForm() {
        initComponents();
        connect();
        loadData();
    }

    private void connect() {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            conn = DriverManager.getConnection(
                "***************************************",
                "root", "root"
            );
        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, "Kết nối CSDL thất bại!");
        }
    }

    private void loadData() {
        try {
            String sql = "SELECT * FROM TL_gvien";
            pst = conn.prepareStatement(sql);
            rs = pst.executeQuery();

            ResultSetMetaData rsmd = rs.getMetaData();
            int cols = rsmd.getColumnCount();

            // Create table model
            Vector column = new Vector();
            column.add("Mã GV");
            column.add("Tên GV");
            column.add("Địa chỉ");
            column.add("Hệ số lương");

            Vector data = new Vector();
            while(rs.next()) {
                Vector row = new Vector();
                for(int i = 1; i <= cols; i++) {
                    row.add(rs.getString(i));
                }
                data.add(row);
            }

            dtm = new DefaultTableModel(data, column);
            tblGiangVien.setModel(dtm);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void clearFields() {
        txtMaGV.setText("");
        txtTenGV.setText("");
        txtDiaChi.setText("");
        txtHeSoLuong.setText("");
        txtMaGV.requestFocus();
    }

    private void showRecord() {
        try {
            if(currentRow >= 0 && currentRow < tblGiangVien.getRowCount()) {
                txtMaGV.setText(tblGiangVien.getValueAt(currentRow, 0).toString());
                txtTenGV.setText(tblGiangVien.getValueAt(currentRow, 1).toString());
                txtDiaChi.setText(tblGiangVien.getValueAt(currentRow, 2).toString());
                txtHeSoLuong.setText(tblGiangVien.getValueAt(currentRow, 3).toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void btnThemActionPerformed(java.awt.event.ActionEvent evt) {
        try {
            String sql = "INSERT INTO TL_gvien VALUES (?, ?, ?, ?)";
            pst = conn.prepareStatement(sql);
            pst.setString(1, txtMaGV.getText());
            pst.setString(2, txtTenGV.getText());
            pst.setString(3, txtDiaChi.getText());
            pst.setFloat(4, Float.parseFloat(txtHeSoLuong.getText()));

            pst.executeUpdate();
            JOptionPane.showMessageDialog(this, "Thêm thành công!");
            loadData();
            clearFields();
        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, "Lỗi: " + e.getMessage());
        }
    }

    private void btnSuaActionPerformed(java.awt.event.ActionEvent evt) {
        try {
            String sql = "UPDATE TL_gvien SET tengv=?, dchi=?, hsluong=? WHERE magv=?";
            pst = conn.prepareStatement(sql);
            pst.setString(1, txtTenGV.getText());
            pst.setString(2, txtDiaChi.getText());
            pst.setFloat(3, Float.parseFloat(txtHeSoLuong.getText()));
            pst.setString(4, txtMaGV.getText());

            pst.executeUpdate();
            JOptionPane.showMessageDialog(this, "Cập nhật thành công!");
            loadData();
        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, "Lỗi: " + e.getMessage());
        }
    }

    private void btnXoaActionPerformed(java.awt.event.ActionEvent evt) {
        try {
            String sql = "DELETE FROM TL_gvien WHERE magv=?";
            pst = conn.prepareStatement(sql);
            pst.setString(1, txtMaGV.getText());

            int choice = JOptionPane.showConfirmDialog(this,
                "Bạn có chắc muốn xóa giảng viên này?",
                "Xác nhận",
                JOptionPane.YES_NO_OPTION);

            if(choice == JOptionPane.YES_OPTION) {
                pst.executeUpdate();
                JOptionPane.showMessageDialog(this, "Xóa thành công!");
                loadData();
                clearFields();
            }
        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, "Lỗi: " + e.getMessage());
        }
    }

    private void btnTimActionPerformed(java.awt.event.ActionEvent evt) {
        try {
            String sql = "SELECT * FROM TL_gvien WHERE magv LIKE ? OR tengv LIKE ?";
            String searchText = "%" + txtMaGV.getText() + "%";
            pst = conn.prepareStatement(sql);
            pst.setString(1, searchText);
            pst.setString(2, searchText);
            rs = pst.executeQuery();

            ResultSetMetaData rsmd = rs.getMetaData();
            int cols = rsmd.getColumnCount();

            Vector column = new Vector();
            column.add("Mã GV");
            column.add("Tên GV");
            column.add("Địa chỉ");
            column.add("Hệ số lương");

            Vector data = new Vector();
            while(rs.next()) {
                Vector row = new Vector();
                for(int i = 1; i <= cols; i++) {
                    row.add(rs.getString(i));
                }
                data.add(row);
            }

            dtm = new DefaultTableModel(data, column);
            tblGiangVien.setModel(dtm);
        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, "Lỗi: " + e.getMessage());
        }
    }

    private void btnSauActionPerformed(java.awt.event.ActionEvent evt) {
        if(currentRow < tblGiangVien.getRowCount() - 1) {
            currentRow++;
            showRecord();
        }
    }

    private void btnTruocActionPerformed(java.awt.event.ActionEvent evt) {
        if(currentRow > 0) {
            currentRow--;
            showRecord();
        }
    }

    private void btnHuyActionPerformed(java.awt.event.ActionEvent evt) {
        clearFields();
    }

    private void btnThoatActionPerformed(java.awt.event.ActionEvent evt) {
        int choice = JOptionPane.showConfirmDialog(this,
            "Bạn có chắc muốn thoát?",
            "Xác nhận",
            JOptionPane.YES_NO_OPTION);

        if(choice == JOptionPane.YES_OPTION) {
            System.exit(0);
        }
    }

    private void tblGiangVienMouseClicked(java.awt.event.MouseEvent evt) {
        currentRow = tblGiangVien.getSelectedRow();
        showRecord();
    }
    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        txtMaGV = new JTextField();
        txtTenGV = new JTextField();
        txtDiaChi = new JTextField();
        txtHeSoLuong = new JTextField();
        tblGiangVien = new JTable();
        btnThem = new JButton("Thêm");
        btnSua = new JButton("Sửa");
        btnXoa = new JButton("Xóa");
        btnHuy = new JButton("Hủy");
        btnThoat = new JButton("Thoát");
        btnTruoc = new JButton("Trước");
        btnSau = new JButton("Sau");
        btnTim = new JButton("Tìm");
        jScrollPane1 = new JScrollPane();
        lblTitle = new JLabel("QUẢN LÝ GIẢNG VIÊN");
        lblMaGV = new JLabel("Mã Giảng Viên:");
        lblTenGV = new JLabel("Tên Giảng Viên:");
        lblDiaChi = new JLabel("Địa Chỉ:");
        lblHeSoLuong = new JLabel("Hệ Số Lương:");

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);
        setTitle("Quản Lý Giảng Viên");

        // Setup table
        jScrollPane1.setViewportView(tblGiangVien);
        tblGiangVien.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                tblGiangVienMouseClicked(evt);
            }
        });

        // Setup button actions
        btnThem.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnThemActionPerformed(evt);
            }
        });

        btnSua.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnSuaActionPerformed(evt);
            }
        });

        btnXoa.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnXoaActionPerformed(evt);
            }
        });

        btnHuy.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnHuyActionPerformed(evt);
            }
        });

        btnThoat.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnThoatActionPerformed(evt);
            }
        });

        btnTruoc.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnTruocActionPerformed(evt);
            }
        });

        btnSau.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnSauActionPerformed(evt);
            }
        });

        btnTim.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnTimActionPerformed(evt);
            }
        });

        // Setup layout
        lblTitle.setFont(new java.awt.Font("Segoe UI", 1, 18));
        lblTitle.setHorizontalAlignment(javax.swing.SwingConstants.CENTER);

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGap(20, 20, 20)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(lblTitle, javax.swing.GroupLayout.DEFAULT_SIZE, 560, Short.MAX_VALUE)
                    .addGroup(layout.createSequentialGroup()
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                            .addComponent(lblMaGV)
                            .addComponent(lblTenGV)
                            .addComponent(lblDiaChi)
                            .addComponent(lblHeSoLuong))
                        .addGap(20, 20, 20)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                            .addComponent(txtMaGV)
                            .addComponent(txtTenGV)
                            .addComponent(txtDiaChi)
                            .addComponent(txtHeSoLuong)))
                    .addComponent(jScrollPane1)
                    .addGroup(layout.createSequentialGroup()
                        .addComponent(btnThem)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(btnSua)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(btnXoa)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(btnTim)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(btnTruoc)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(btnSau)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(btnHuy)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(btnThoat)))
                .addGap(20, 20, 20))
        );

        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGap(20, 20, 20)
                .addComponent(lblTitle)
                .addGap(20, 20, 20)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(lblMaGV)
                    .addComponent(txtMaGV, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(10, 10, 10)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(lblTenGV)
                    .addComponent(txtTenGV, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(10, 10, 10)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(lblDiaChi)
                    .addComponent(txtDiaChi, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(10, 10, 10)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(lblHeSoLuong)
                    .addComponent(txtHeSoLuong, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(20, 20, 20)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(btnThem)
                    .addComponent(btnSua)
                    .addComponent(btnXoa)
                    .addComponent(btnTim)
                    .addComponent(btnTruoc)
                    .addComponent(btnSau)
                    .addComponent(btnHuy)
                    .addComponent(btnThoat))
                .addGap(20, 20, 20)
                .addComponent(jScrollPane1, javax.swing.GroupLayout.DEFAULT_SIZE, 200, Short.MAX_VALUE)
                .addGap(20, 20, 20))
        );

        pack();
        setLocationRelativeTo(null);
    }// </editor-fold>//GEN-END:initComponents

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(QuanLyGiangVienForm.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(QuanLyGiangVienForm.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(QuanLyGiangVienForm.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(QuanLyGiangVienForm.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new QuanLyGiangVienForm().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton btnHuy;
    private javax.swing.JButton btnSau;
    private javax.swing.JButton btnSua;
    private javax.swing.JButton btnThem;
    private javax.swing.JButton btnThoat;
    private javax.swing.JButton btnTim;
    private javax.swing.JButton btnTruoc;
    private javax.swing.JButton btnXoa;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JLabel lblDiaChi;
    private javax.swing.JLabel lblHeSoLuong;
    private javax.swing.JLabel lblMaGV;
    private javax.swing.JLabel lblTenGV;
    private javax.swing.JLabel lblTitle;
    private javax.swing.JTable tblGiangVien;
    private javax.swing.JTextField txtDiaChi;
    private javax.swing.JTextField txtHeSoLuong;
    private javax.swing.JTextField txtMaGV;
    private javax.swing.JTextField txtTenGV;
    // End of variables declaration//GEN-END:variables
}
